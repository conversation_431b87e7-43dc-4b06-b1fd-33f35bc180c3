// deno-lint-ignore-file verbatim-module-syntax
import { <PERSON><PERSON> } from "@hono/hono";
import { cors } from "hono/cors";
import { auth } from "./utils/auth.ts";
import { db } from "./database/db.ts";
import { projects, projectMembers, tasks, submissions } from "./drizzle/schema.ts";
import { eq, and, desc, count } from "drizzle-orm";

const app = new Hono();

// Enable CORS for frontend
app.use("/*", cors({
  origin: ["http://localhost:3000"],
  allowMethods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
  allowHeaders: ["Content-Type", "Authorization"],
  credentials: true,
}));

// Auth routes
app.on(["POST", "GET"], "/api/auth/**", (c) => auth.handler(c.req.raw));

// Helper function to get user from session
async function getUserFromSession(c: any) {
  const session = await auth.api.getSession({
    headers: c.req.header(),
  });
  return session?.user || null;
}

// Get public projects
app.get("/api/projects", async (c) => {
  try {
    const publicProjects = await db
      .select({
        id: projects.id,
        name: projects.name,
        description: projects.description,
        type: projects.type,
        dialect: projects.dialect,
        license: projects.license,
        visibility: projects.visibility,
        status: projects.status,
        createdAt: projects.createdAt,
        createdBy: projects.createdBy,
      })
      .from(projects)
      .where(eq(projects.visibility, "public"))
      .orderBy(desc(projects.createdAt));

    // Get member counts for each project
    const projectsWithStats = await Promise.all(
      publicProjects.map(async (project) => {
        const memberCount = await db
          .select({ count: count() })
          .from(projectMembers)
          .where(eq(projectMembers.projectId, project.id));

        const taskCount = await db
          .select({ count: count() })
          .from(tasks)
          .where(eq(tasks.projectId, project.id));

        const submissionCount = await db
          .select({ count: count() })
          .from(submissions)
          .innerJoin(tasks, eq(submissions.taskId, tasks.id))
          .where(eq(tasks.projectId, project.id));

        return {
          ...project,
          members: memberCount[0]?.count || 0,
          totalSentences: taskCount[0]?.count || 0,
          recordings: submissionCount[0]?.count || 0,
        };
      })
    );

    return c.json(projectsWithStats);
  } catch (error) {
    console.error("Error fetching projects:", error);
    return c.json({ error: "Failed to fetch projects" }, 500);
  }
});

// Get user's projects (requires authentication)
app.get("/api/user/projects", async (c) => {
  try {
    const user = await getUserFromSession(c);
    if (!user) {
      return c.json({ error: "Unauthorized" }, 401);
    }

    const userProjects = await db
      .select({
        id: projects.id,
        name: projects.name,
        description: projects.description,
        type: projects.type,
        dialect: projects.dialect,
        license: projects.license,
        visibility: projects.visibility,
        status: projects.status,
        createdAt: projects.createdAt,
      })
      .from(projects)
      .where(eq(projects.createdBy, user.id))
      .orderBy(desc(projects.createdAt));

    // Get stats for each project
    const projectsWithStats = await Promise.all(
      userProjects.map(async (project) => {
        const memberCount = await db
          .select({ count: count() })
          .from(projectMembers)
          .where(eq(projectMembers.projectId, project.id));

        const taskCount = await db
          .select({ count: count() })
          .from(tasks)
          .where(eq(tasks.projectId, project.id));

        const submissionCount = await db
          .select({ count: count() })
          .from(submissions)
          .innerJoin(tasks, eq(submissions.taskId, tasks.id))
          .where(eq(tasks.projectId, project.id));

        return {
          ...project,
          members: memberCount[0]?.count || 0,
          totalSentences: taskCount[0]?.count || 0,
          recordings: submissionCount[0]?.count || 0,
        };
      })
    );

    return c.json(projectsWithStats);
  } catch (error) {
    console.error("Error fetching user projects:", error);
    return c.json({ error: "Failed to fetch user projects" }, 500);
  }
});

// Create a new project (requires authentication)
app.post("/api/projects", async (c) => {
  try {
    const user = await getUserFromSession(c);
    if (!user) {
      return c.json({ error: "Unauthorized" }, 401);
    }

    const body = await c.req.json();
    const { name, description, language, dialect, type, visibility, license } = body;

    // Validate required fields
    if (!name || !type || !visibility) {
      return c.json({ error: "Missing required fields" }, 400);
    }

    // Validate enum values
    const validTypes = ["audio", "text", "translation", "training"];
    const validVisibilities = ["public", "private"];
    const validStatuses = ["in_progress", "paused", "closed"];

    if (!validTypes.includes(type)) {
      return c.json({ error: "Invalid project type" }, 400);
    }

    if (!validVisibilities.includes(visibility)) {
      return c.json({ error: "Invalid visibility" }, 400);
    }

    // Create the project
    const newProject = await db
      .insert(projects)
      .values({
        name,
        description: description || null,
        type,
        dialect: dialect || null,
        license: license || "CC0 - Public Domain",
        visibility,
        status: "in_progress",
        createdBy: user.id,
      })
      .returning();

    // Add the creator as an admin member
    await db.insert(projectMembers).values({
      projectId: newProject[0].id,
      userId: user.id,
      role: "admin",
    });

    return c.json(newProject[0], 201);
  } catch (error) {
    console.error("Error creating project:", error);
    return c.json({ error: "Failed to create project" }, 500);
  }
});

// Get a specific project by ID
app.get("/api/projects/:id", async (c) => {
  try {
    const projectId = parseInt(c.req.param("id"));
    if (isNaN(projectId)) {
      return c.json({ error: "Invalid project ID" }, 400);
    }

    const project = await db
      .select()
      .from(projects)
      .where(eq(projects.id, projectId))
      .limit(1);

    if (project.length === 0) {
      return c.json({ error: "Project not found" }, 404);
    }

    // Check if project is public or user has access
    const user = await getUserFromSession(c);
    if (project[0].visibility === "private") {
      if (!user) {
        return c.json({ error: "Unauthorized" }, 401);
      }

      // Check if user is a member or owner
      const membership = await db
        .select()
        .from(projectMembers)
        .where(
          and(
            eq(projectMembers.projectId, projectId),
            eq(projectMembers.userId, user.id)
          )
        )
        .limit(1);

      if (membership.length === 0 && project[0].createdBy !== user.id) {
        return c.json({ error: "Access denied" }, 403);
      }
    }

    // Get project stats
    const memberCount = await db
      .select({ count: count() })
      .from(projectMembers)
      .where(eq(projectMembers.projectId, projectId));

    const taskCount = await db
      .select({ count: count() })
      .from(tasks)
      .where(eq(tasks.projectId, projectId));

    const submissionCount = await db
      .select({ count: count() })
      .from(submissions)
      .innerJoin(tasks, eq(submissions.taskId, tasks.id))
      .where(eq(tasks.projectId, projectId));

    return c.json({
      ...project[0],
      members: memberCount[0]?.count || 0,
      totalSentences: taskCount[0]?.count || 0,
      recordings: submissionCount[0]?.count || 0,
    });
  } catch (error) {
    console.error("Error fetching project:", error);
    return c.json({ error: "Failed to fetch project" }, 500);
  }
});

app.get("/", (c) => c.text("Welcome to Kallama Backend!"));

Deno.serve(app.fetch);