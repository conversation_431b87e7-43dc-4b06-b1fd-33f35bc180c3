// deno-lint-ignore-file verbatim-module-syntax
import { <PERSON><PERSON> } from "hono";
import { cors } from "hono/cors";
import { auth } from "./utils/auth.ts";
import { db } from "./database/db.ts";
import { eq, and, desc, count } from "drizzle-orm";

const app = new Hono();

// Enable CORS for frontend
app.use("/*", cors({
  origin: ["http://localhost:3000"],
  allowMethods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
  allowHeaders: ["Content-Type", "Authorization"],
  credentials: true,
}));

// Auth routes
app.on(["POST", "GET"], "/api/auth/**", (c) => auth.handler(c.req.raw));

// Helper function to get user from session
async function getUserFromSession(c: any) {
  const session = await auth.api.getSession({
    headers: c.req.header(),
  });
  return session?.user || null;
}

// Mock data for development
const mockProjects = [
  {
    id: 1,
    name: "Yoruba Voice Collection",
    description: "Help build the largest open-source Yoruba speech dataset for AI applications",
    language: "Yoruba",
    dialect: "Lagos",
    type: "audio",
    license: "CC0 - Public Domain",
    visibility: "public",
    status: "in_progress",
    createdAt: "2024-01-15T00:00:00Z",
    createdBy: "user1",
    members: 245,
    totalSentences: 150,
    recordings: 125
  },
  {
    id: 2,
    name: "Swahili Text Translation",
    description: "Contribute to translating common phrases and sentences into Swahili",
    language: "Swahili",
    dialect: "Standard",
    type: "translation",
    license: "CC BY 4.0",
    visibility: "public",
    status: "in_progress",
    createdAt: "2024-02-01T00:00:00Z",
    createdBy: "user2",
    members: 189,
    totalSentences: 200,
    recordings: 89
  },
  {
    id: 3,
    name: "Hausa Storytelling",
    description: "Record traditional Hausa stories and folktales for cultural preservation",
    language: "Hausa",
    dialect: "Kano",
    type: "audio",
    license: "CC0 - Public Domain",
    visibility: "public",
    status: "in_progress",
    createdAt: "2024-02-15T00:00:00Z",
    createdBy: "user3",
    members: 156,
    totalSentences: 100,
    recordings: 34
  },
  {
    id: 4,
    name: "Amharic Dictionary",
    description: "Build a comprehensive digital dictionary with audio pronunciations",
    language: "Amharic",
    dialect: "Addis Ababa",
    type: "text",
    license: "CC BY-SA 4.0",
    visibility: "public",
    status: "in_progress",
    createdAt: "2024-03-01T00:00:00Z",
    createdBy: "user4",
    members: 78,
    totalSentences: 300,
    recordings: 156
  }
];

// Get public projects
app.get("/api/projects", async (c) => {
  try {
    console.log("Fetching public projects...");

    // Use mock data for now
    const publicProjects = mockProjects.filter(p => p.visibility === "public");

    console.log(`Found ${publicProjects.length} public projects`);
    return c.json(publicProjects);
  } catch (error) {
    console.error("Error fetching projects:", error);
    return c.json({ error: "Failed to fetch projects" }, 500);
  }
});

// Get user's projects (requires authentication)
app.get("/api/user/projects", async (c) => {
  try {
    const user = await getUserFromSession(c);
    if (!user) {
      return c.json({ error: "Unauthorized" }, 401);
    }

    const userProjects = await db
      .select({
        id: projects.id,
        name: projects.name,
        description: projects.description,
        type: projects.type,
        dialect: projects.dialect,
        license: projects.license,
        visibility: projects.visibility,
        status: projects.status,
        createdAt: projects.createdAt,
      })
      .from(projects)
      .where(eq(projects.createdBy, user.id))
      .orderBy(desc(projects.createdAt));

    // Get stats for each project
    const projectsWithStats = await Promise.all(
      userProjects.map(async (project) => {
        const memberCount = await db
          .select({ count: count() })
          .from(projectMembers)
          .where(eq(projectMembers.projectId, project.id));

        const taskCount = await db
          .select({ count: count() })
          .from(tasks)
          .where(eq(tasks.projectId, project.id));

        const submissionCount = await db
          .select({ count: count() })
          .from(submissions)
          .innerJoin(tasks, eq(submissions.taskId, tasks.id))
          .where(eq(tasks.projectId, project.id));

        return {
          ...project,
          members: memberCount[0]?.count || 0,
          totalSentences: taskCount[0]?.count || 0,
          recordings: submissionCount[0]?.count || 0,
        };
      })
    );

    return c.json(projectsWithStats);
  } catch (error) {
    console.error("Error fetching user projects:", error);
    return c.json({ error: "Failed to fetch user projects" }, 500);
  }
});

// Create a new project (requires authentication)
app.post("/api/projects", async (c) => {
  try {
    const user = await getUserFromSession(c);
    if (!user) {
      return c.json({ error: "Unauthorized" }, 401);
    }

    const body = await c.req.json();
    const { name, description, language, dialect, type, visibility, license } = body;

    // Validate required fields
    if (!name || !type || !visibility) {
      return c.json({ error: "Missing required fields" }, 400);
    }

    // Validate enum values
    const validTypes = ["audio", "text", "translation", "training"];
    const validVisibilities = ["public", "private"];
    const validStatuses = ["in_progress", "paused", "closed"];

    if (!validTypes.includes(type)) {
      return c.json({ error: "Invalid project type" }, 400);
    }

    if (!validVisibilities.includes(visibility)) {
      return c.json({ error: "Invalid visibility" }, 400);
    }

    // Create the project
    const newProject = await db
      .insert(projects)
      .values({
        name,
        description: description || null,
        type,
        dialect: dialect || null,
        license: license || "CC0 - Public Domain",
        visibility,
        status: "in_progress",
        createdBy: user.id,
      })
      .returning();

    // Add the creator as an admin member
    await db.insert(projectMembers).values({
      projectId: newProject[0].id,
      userId: user.id,
      role: "admin",
    });

    return c.json(newProject[0], 201);
  } catch (error) {
    console.error("Error creating project:", error);
    return c.json({ error: "Failed to create project" }, 500);
  }
});

// Get a specific project by ID
app.get("/api/projects/:id", async (c) => {
  try {
    const projectId = parseInt(c.req.param("id"));
    if (isNaN(projectId)) {
      return c.json({ error: "Invalid project ID" }, 400);
    }

    const project = await db
      .select()
      .from(projects)
      .where(eq(projects.id, projectId))
      .limit(1);

    if (project.length === 0) {
      return c.json({ error: "Project not found" }, 404);
    }

    // Check if project is public or user has access
    const user = await getUserFromSession(c);
    if (project[0].visibility === "private") {
      if (!user) {
        return c.json({ error: "Unauthorized" }, 401);
      }

      // Check if user is a member or owner
      const membership = await db
        .select()
        .from(projectMembers)
        .where(
          and(
            eq(projectMembers.projectId, projectId),
            eq(projectMembers.userId, user.id)
          )
        )
        .limit(1);

      if (membership.length === 0 && project[0].createdBy !== user.id) {
        return c.json({ error: "Access denied" }, 403);
      }
    }

    // Get project stats
    const memberCount = await db
      .select({ count: count() })
      .from(projectMembers)
      .where(eq(projectMembers.projectId, projectId));

    const taskCount = await db
      .select({ count: count() })
      .from(tasks)
      .where(eq(tasks.projectId, projectId));

    const submissionCount = await db
      .select({ count: count() })
      .from(submissions)
      .innerJoin(tasks, eq(submissions.taskId, tasks.id))
      .where(eq(tasks.projectId, projectId));

    return c.json({
      ...project[0],
      members: memberCount[0]?.count || 0,
      totalSentences: taskCount[0]?.count || 0,
      recordings: submissionCount[0]?.count || 0,
    });
  } catch (error) {
    console.error("Error fetching project:", error);
    return c.json({ error: "Failed to fetch project" }, 500);
  }
});

// Health check endpoint
app.get("/health", async (c) => {
  try {
    // Test database connection
    const result = await db.select().from(projects).limit(1);
    return c.json({
      status: "healthy",
      database: "connected",
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error("Health check failed:", error);
    return c.json({
      status: "unhealthy",
      database: "disconnected",
      error: error.message,
      timestamp: new Date().toISOString()
    }, 500);
  }
});

app.get("/", (c) => c.text("Welcome to Kallama Backend!"));

Deno.serve(app.fetch);