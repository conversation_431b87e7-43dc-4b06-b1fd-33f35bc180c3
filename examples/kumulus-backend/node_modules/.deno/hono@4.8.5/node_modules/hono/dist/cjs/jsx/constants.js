"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
var constants_exports = {};
__export(constants_exports, {
  DOM_ERROR_HANDLER: () => DOM_ERROR_HANDLER,
  DOM_INTERNAL_TAG: () => DOM_INTERNAL_TAG,
  DOM_MEMO: () => DOM_MEMO,
  DOM_RENDERER: () => DOM_RENDERER,
  DOM_STASH: () => DOM_STASH,
  PERMALINK: () => PERMALINK
});
module.exports = __toCommonJS(constants_exports);
const DOM_RENDERER = Symbol("RENDERER");
const DOM_ERROR_HANDLER = Symbol("ERROR_HANDLER");
const DOM_STASH = Symbol("STASH");
const DOM_INTERNAL_TAG = Symbol("INTERNAL");
const DOM_MEMO = Symbol("MEMO");
const PERMALINK = Symbol("PERMALINK");
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  DOM_ERROR_HANDLER,
  DOM_INTERNAL_TAG,
  DOM_MEMO,
  DOM_RENDERER,
  DOM_STASH,
  PERMALINK
});
