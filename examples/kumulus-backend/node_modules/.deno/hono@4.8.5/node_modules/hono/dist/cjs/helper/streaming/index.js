"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
var streaming_exports = {};
__export(streaming_exports, {
  SSEStreamingApi: () => import_sse.SSEStreamingApi,
  stream: () => import_stream.stream,
  streamSSE: () => import_sse.streamSSE,
  streamText: () => import_text.streamText
});
module.exports = __toCommonJS(streaming_exports);
var import_stream = require("./stream");
var import_sse = require("./sse");
var import_text = require("./text");
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  SSEStreamingApi,
  stream,
  streamSSE,
  streamText
});
