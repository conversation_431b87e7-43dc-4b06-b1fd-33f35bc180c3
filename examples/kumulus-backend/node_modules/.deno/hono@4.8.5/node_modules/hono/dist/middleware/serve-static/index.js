// src/middleware/serve-static/index.ts
import { COMPRESSIBLE_CONTENT_TYPE_REGEX } from "../../utils/compress.js";
import { getFilePath, getFilePathWithoutDefaultDocument } from "../../utils/filepath.js";
import { getMimeType } from "../../utils/mime.js";
var ENCODINGS = {
  br: ".br",
  zstd: ".zst",
  gzip: ".gz"
};
var ENCODINGS_ORDERED_KEYS = Object.keys(ENCODINGS);
var DEFAULT_DOCUMENT = "index.html";
var defaultPathResolve = (path) => path;
var isAbsolutePath = (path) => {
  const isUnixAbsolutePath = path.startsWith("/");
  const hasDriveLetter = /^[a-zA-Z]:\\/.test(path);
  const isUncPath = /^\\\\[^\\]+\\[^\\]+/.test(path);
  return isUnixAbsolutePath || hasDriveLetter || isUncPath;
};
var windowsPathToUnixPath = (path) => {
  return path.replace(/^[a-zA-Z]:/, "").replace(/\\/g, "/");
};
var serveStatic = (options) => {
  let isAbsoluteRoot = false;
  let root;
  if (options.root) {
    if (isAbsolutePath(options.root)) {
      isAbsoluteRoot = true;
      root = windowsPathToUnixPath(options.root);
      root = new URL(`file://${root}`).pathname;
    } else {
      root = options.root;
    }
  }
  return async (c, next) => {
    if (c.finalized) {
      await next();
      return;
    }
    let filename = options.path ?? decodeURI(c.req.path);
    filename = options.rewriteRequestPath ? options.rewriteRequestPath(filename) : filename;
    if (!filename.endsWith("/") && options.isDir) {
      const path2 = getFilePathWithoutDefaultDocument({
        filename,
        root
      });
      if (path2 && await options.isDir(path2)) {
        filename += "/";
      }
    }
    let path = getFilePath({
      filename,
      root,
      defaultDocument: DEFAULT_DOCUMENT
    });
    if (!path) {
      return await next();
    }
    if (isAbsoluteRoot) {
      path = "/" + path;
    }
    const getContent = options.getContent;
    const pathResolve = options.pathResolve ?? defaultPathResolve;
    path = pathResolve(path);
    let content = await getContent(path, c);
    if (!content) {
      let pathWithoutDefaultDocument = getFilePathWithoutDefaultDocument({
        filename,
        root
      });
      if (!pathWithoutDefaultDocument) {
        return await next();
      }
      pathWithoutDefaultDocument = pathResolve(pathWithoutDefaultDocument);
      if (pathWithoutDefaultDocument !== path) {
        content = await getContent(pathWithoutDefaultDocument, c);
        if (content) {
          path = pathWithoutDefaultDocument;
        }
      }
    }
    if (content instanceof Response) {
      return c.newResponse(content.body, content);
    }
    if (content) {
      const mimeType = options.mimes && getMimeType(path, options.mimes) || getMimeType(path);
      c.header("Content-Type", mimeType || "application/octet-stream");
      if (options.precompressed && (!mimeType || COMPRESSIBLE_CONTENT_TYPE_REGEX.test(mimeType))) {
        const acceptEncodingSet = new Set(
          c.req.header("Accept-Encoding")?.split(",").map((encoding) => encoding.trim())
        );
        for (const encoding of ENCODINGS_ORDERED_KEYS) {
          if (!acceptEncodingSet.has(encoding)) {
            continue;
          }
          const compressedContent = await getContent(path + ENCODINGS[encoding], c);
          if (compressedContent) {
            content = compressedContent;
            c.header("Content-Encoding", encoding);
            c.header("Vary", "Accept-Encoding", { append: true });
            break;
          }
        }
      }
      await options.onFound?.(path, c);
      return c.body(content);
    }
    await options.onNotFound?.(path, c);
    await next();
    return;
  };
};
export {
  serveStatic
};
