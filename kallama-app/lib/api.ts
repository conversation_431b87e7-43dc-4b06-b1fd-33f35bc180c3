const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';

// Helper function to make authenticated requests
async function apiRequest(endpoint: string, options: RequestInit = {}) {
  const url = `${API_BASE_URL}${endpoint}`;
  
  const defaultOptions: RequestInit = {
    headers: {
      'Content-Type': 'application/json',
      ...options.headers,
    },
    credentials: 'include', // Include cookies for authentication
    ...options,
  };

  const response = await fetch(url, defaultOptions);
  
  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ error: 'Unknown error' }));
    throw new Error(errorData.error || `HTTP ${response.status}`);
  }

  return response.json();
}

// Project API functions
export const projectsApi = {
  // Get all public projects
  getPublicProjects: async () => {
    return apiRequest('/api/projects');
  },

  // Get user's projects (requires authentication)
  getUserProjects: async () => {
    return apiRequest('/api/user/projects');
  },

  // Get a specific project by ID
  getProject: async (id: string | number) => {
    return apiRequest(`/api/projects/${id}`);
  },

  // Create a new project (requires authentication)
  createProject: async (projectData: {
    name: string;
    description?: string;
    language: string;
    dialect?: string;
    type: string;
    visibility: string;
    license?: string;
  }) => {
    return apiRequest('/api/projects', {
      method: 'POST',
      body: JSON.stringify(projectData),
    });
  },

  // Update a project (requires authentication and ownership)
  updateProject: async (id: string | number, projectData: Partial<{
    name: string;
    description: string;
    language: string;
    dialect: string;
    type: string;
    visibility: string;
    license: string;
    status: string;
  }>) => {
    return apiRequest(`/api/projects/${id}`, {
      method: 'PUT',
      body: JSON.stringify(projectData),
    });
  },

  // Delete a project (requires authentication and ownership)
  deleteProject: async (id: string | number) => {
    return apiRequest(`/api/projects/${id}`, {
      method: 'DELETE',
    });
  },
};

// User API functions
export const userApi = {
  // Get current user profile
  getProfile: async () => {
    return apiRequest('/api/user/profile');
  },

  // Update user profile
  updateProfile: async (profileData: {
    name?: string;
    email?: string;
  }) => {
    return apiRequest('/api/user/profile', {
      method: 'PUT',
      body: JSON.stringify(profileData),
    });
  },
};

// Types for better TypeScript support
export interface Project {
  id: number;
  name: string;
  description?: string;
  language?: string;
  dialect?: string;
  type: 'audio' | 'text' | 'translation' | 'training';
  visibility: 'public' | 'private';
  status: 'in_progress' | 'paused' | 'closed';
  license?: string;
  createdBy: string;
  createdAt: string;
  members: number;
  totalSentences: number;
  recordings: number;
}

export interface User {
  id: string;
  name: string;
  email: string;
  emailVerified: boolean;
  image?: string;
  createdAt: string;
  updatedAt: string;
}
