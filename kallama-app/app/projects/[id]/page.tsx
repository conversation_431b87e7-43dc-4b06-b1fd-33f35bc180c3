'use client';

import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import Layout from '@/components/Layout';
import { useParams } from 'next/navigation';
import { useSession } from '@/lib/auth-client';
import { Settings, Users, FileText, Play, Mic } from 'lucide-react';
import { projectsApi, type Project } from '@/lib/api';

export default function ProjectDetailsPage() {
  const params = useParams();
  const { data: session } = useSession();
  const [activeTab, setActiveTab] = useState('overview');
  const [project, setProject] = useState<Project | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');

  // Fetch project data
  useEffect(() => {
    const fetchProject = async () => {
      try {
        setIsLoading(true);
        const data = await projectsApi.getProject(params.id as string);
        setProject(data);
      } catch (err: any) {
        setError(err.message || 'Failed to fetch project');
        console.error('Error fetching project:', err);
      } finally {
        setIsLoading(false);
      }
    };

    if (params.id) {
      fetchProject();
    }
  }, [params.id]);

  const sentences = [
    { id: 1, text: "Bawo ni o se wa?", recorded: true, recordings: 3 },
    { id: 2, text: "Mo wa daadaa, e se", recorded: true, recordings: 5 },
    { id: 3, text: "Kini oruko re?", recorded: false, recordings: 0 },
    { id: 4, text: "Mo n lo si oja", recorded: true, recordings: 2 },
    { id: 5, text: "Owo eya ni?", recorded: false, recordings: 0 },
  ];

  const members = [
    { id: 1, name: "John Doe", role: "admin", joinedAt: "2024-01-15", contributions: 25 },
    { id: 2, name: "Jane Smith", role: "contributor", joinedAt: "2024-01-20", contributions: 15 },
    { id: 3, name: "Mike Johnson", role: "contributor", joinedAt: "2024-02-01", contributions: 8 },
  ];

  if (isLoading) {
    return (
      <Layout showNavigation>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
            <p className="mt-2 text-muted-foreground">Loading project...</p>
          </div>
        </div>
      </Layout>
    );
  }

  if (error) {
    return (
      <Layout showNavigation>
        <div className="text-center space-y-4">
          <h1 className="text-2xl font-bold">Error Loading Project</h1>
          <p className="text-red-500">{error}</p>
          <Button onClick={() => window.location.reload()}>
            Try Again
          </Button>
        </div>
      </Layout>
    );
  }

  if (!project) {
    return (
      <Layout showNavigation>
        <div className="text-center space-y-4">
          <h1 className="text-2xl font-bold">Project Not Found</h1>
          <p className="text-muted-foreground">The project you're looking for doesn't exist.</p>
        </div>
      </Layout>
    );
  }

  return (
    <Layout showNavigation>
      <div className="max-w-6xl mx-auto space-y-6">
        {/* Project Header */}
        <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
          <div className="space-y-2">
            <div className="flex items-center gap-3">
              <h1 className="text-3xl font-bold text-primary">{project.name}</h1>
              <Badge variant={project.visibility === "public" ? "default" : "secondary"}>
                {project.visibility === "public" ? "Public" : "Private"}
              </Badge>
              <Badge variant="outline" className="capitalize">{project.type}</Badge>
            </div>
            <p className="text-muted-foreground">{project.description}</p>
            <div className="flex flex-wrap gap-4 text-sm text-muted-foreground">
              {project.language && <span>Language: {project.language}</span>}
              {project.dialect && <span>Dialect: {project.dialect}</span>}
              <span>Created: {new Date(project.createdAt).toLocaleDateString()}</span>
              <span>Owner: {project.createdBy}</span>
            </div>
          </div>
          <div className="flex gap-2">
            <Button variant="outline">Export Data</Button>
            <Button className="flex items-center gap-2">
              <Mic className="w-4 h-4" />
              Start Recording
            </Button>
          </div>
        </div>

        {/* Project Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Total Sentences</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{project.totalSentences}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Recorded</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-primary">{project.recordings}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Progress</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {project.totalSentences > 0 ? Math.round((project.recordings / project.totalSentences) * 100) : 0}%
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Members</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{project.members}</div>
            </CardContent>
          </Card>
        </div>

        {/* Project Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="sentences">Sentences</TabsTrigger>
            <TabsTrigger value="members">Members</TabsTrigger>
            <TabsTrigger value="settings">Settings</TabsTrigger>
          </TabsList>

          <TabsContent value="overview">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Project Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <h4 className="font-medium">Description</h4>
                    <p className="text-sm text-muted-foreground">{project.description}</p>
                  </div>
                  <div>
                    <h4 className="font-medium">Language & Dialect</h4>
                    <p className="text-sm text-muted-foreground">{project.language} - {project.dialect}</p>
                  </div>
                  <div>
                    <h4 className="font-medium">License</h4>
                    <p className="text-sm text-muted-foreground">{project.license}</p>
                  </div>
                  <div>
                    <h4 className="font-medium">Status</h4>
                    <Badge variant="outline" className="capitalize">
                      {project.status.replace('_', ' ')}
                    </Badge>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Recent Activity</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex items-center gap-3">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      <span className="text-sm">Jane Smith recorded 3 new sentences</span>
                      <span className="text-xs text-muted-foreground ml-auto">2h ago</span>
                    </div>
                    <div className="flex items-center gap-3">
                      <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                      <span className="text-sm">Mike Johnson joined the project</span>
                      <span className="text-xs text-muted-foreground ml-auto">1d ago</span>
                    </div>
                    <div className="flex items-center gap-3">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      <span className="text-sm">John Doe added 10 new sentences</span>
                      <span className="text-xs text-muted-foreground ml-auto">2d ago</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="sentences">
            <Card>
              <CardHeader>
                <CardTitle>Project Sentences</CardTitle>
                <CardDescription>
                  Sentences available for recording in this project
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {sentences.map((sentence) => (
                    <div key={sentence.id} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex-1">
                        <p className="font-medium">{sentence.text}</p>
                        <p className="text-sm text-muted-foreground">
                          {sentence.recordings} recording{sentence.recordings !== 1 ? 's' : ''}
                        </p>
                      </div>
                      <div className="flex items-center gap-2">
                        {sentence.recorded && (
                          <Badge variant="outline" className="text-green-600">
                            Recorded
                          </Badge>
                        )}
                        <Button size="sm" variant="outline">
                          <Play className="w-4 h-4" />
                        </Button>
                        <Button size="sm">
                          <Mic className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="members">
            <Card>
              <CardHeader>
                <CardTitle>Project Members</CardTitle>
                <CardDescription>
                  People contributing to this project
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {members.map((member) => (
                    <div key={member.id} className="flex items-center justify-between p-3 border rounded-lg">
                      <div>
                        <p className="font-medium">{member.name}</p>
                        <p className="text-sm text-muted-foreground">
                          {member.contributions} contributions • Joined {member.joinedAt}
                        </p>
                      </div>
                      <Badge variant={member.role === 'admin' ? 'default' : 'outline'} className="capitalize">
                        {member.role}
                      </Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="settings">
            <Card>
              <CardHeader>
                <CardTitle>Project Settings</CardTitle>
                <CardDescription>
                  Manage your project configuration
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <p className="text-muted-foreground">
                    Project settings will be available here. This includes visibility, member permissions, 
                    data export options, and project archival.
                  </p>
                  <Button variant="outline" className="flex items-center gap-2">
                    <Settings className="w-4 h-4" />
                    Configure Settings
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </Layout>
  );
}
