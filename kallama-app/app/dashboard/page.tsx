'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Plus, Settings, Users, FileText } from 'lucide-react';
import Layout from '@/components/Layout';
import Link from 'next/link';
import { useSession } from '@/lib/auth-client';
import { projectsApi, type Project } from '@/lib/api';

export default function DashboardPage() {
  const { data: session, isPending } = useSession();
  const [userProjects, setUserProjects] = useState<Project[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');

  // Fetch user projects when session is available
  useEffect(() => {
    const fetchUserProjects = async () => {
      if (!session) return;

      try {
        setIsLoading(true);
        const data = await projectsApi.getUserProjects();
        setUserProjects(data);
      } catch (err: any) {
        setError(err.message || 'Failed to fetch projects');
        console.error('Error fetching user projects:', err);
      } finally {
        setIsLoading(false);
      }
    };

    if (session) {
      fetchUserProjects();
    } else if (!isPending) {
      setIsLoading(false);
    }
  }, [session, isPending]);

  if (isPending || isLoading) {
    return (
      <Layout showNavigation>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
            <p className="mt-2 text-muted-foreground">Loading...</p>
          </div>
        </div>
      </Layout>
    );
  }

  if (!session) {
    return (
      <Layout showNavigation>
        <div className="text-center space-y-4">
          <h1 className="text-2xl font-bold">Please sign in to access your dashboard</h1>
          <Link href="/auth/signin">
            <Button>Sign In</Button>
          </Link>
        </div>
      </Layout>
    );
  }

  return (
    <Layout showNavigation>
      <div className="space-y-8">
        {/* Welcome Section */}
        <div className="space-y-2">
          <h1 className="text-3xl font-bold text-primary">Welcome back, {session.user.name}!</h1>
          <p className="text-muted-foreground">
            Continue your journey in preserving African languages
          </p>
        </div>

        {error && (
          <div className="text-center">
            <div className="text-red-500 mb-4">{error}</div>
            <Button onClick={() => window.location.reload()}>
              Try Again
            </Button>
          </div>
        )}

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium flex items-center gap-2">
                <FileText className="w-4 h-4" />
                My Projects
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{userProjects.length}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium flex items-center gap-2">
                <Users className="w-4 h-4" />
                Total Members
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {userProjects.reduce((sum, project) => sum + project.members, 0)}
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Total Recordings</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-primary">
                {userProjects.reduce((sum, project) => sum + project.recordings, 0)}
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Completion Rate</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {userProjects.length > 0 ? Math.round(
                  (userProjects.reduce((sum, project) => sum + project.recordings, 0) /
                   userProjects.reduce((sum, project) => sum + project.totalSentences, 0)) * 100
                ) : 0}%
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Action Buttons */}
        <div className="flex gap-4">
          <Link href="/projects/create">
            <Button size="lg" className="flex items-center gap-2">
              <Plus className="w-4 h-4" />
              Create New Project
            </Button>
          </Link>
          <Button variant="outline" size="lg" className="flex items-center gap-2">
            <Settings className="w-4 h-4" />
            Settings
          </Button>
        </div>

        {/* My Projects */}
        <div className="space-y-4">
          <h2 className="text-2xl font-semibold">My Projects</h2>
          {userProjects.length === 0 ? (
            <Card>
              <CardContent className="text-center py-8">
                <p className="text-muted-foreground mb-4">You haven't created any projects yet.</p>
                <Link href="/projects/create">
                  <Button>Create Your First Project</Button>
                </Link>
              </CardContent>
            </Card>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {userProjects.map((project) => (
                <Card key={project.id} className="hover:shadow-lg transition-shadow">
                  <CardHeader>
                    <div className="flex justify-between items-start">
                      <CardTitle className="text-lg">{project.name}</CardTitle>
                      <div className="flex gap-1">
                        <Badge variant={project.visibility === 'public' ? 'default' : 'secondary'}>
                          {project.visibility}
                        </Badge>
                        <Badge variant="outline">{project.type}</Badge>
                      </div>
                    </div>
                    <CardDescription className="text-sm">
                      {project.description}
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex flex-wrap gap-2">
                      <Badge variant="outline">{project.language}</Badge>
                      <Badge variant="outline" className="capitalize">{project.status.replace('_', ' ')}</Badge>
                    </div>
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>Progress</span>
                        <span>{Math.round((project.recordings / project.totalSentences) * 100)}%</span>
                      </div>
                      <div className="w-full bg-muted rounded-full h-2">
                        <div 
                          className="bg-primary h-2 rounded-full" 
                          style={{ width: `${(project.recordings / project.totalSentences) * 100}%` }}
                        ></div>
                      </div>
                    </div>
                    <div className="flex justify-between text-sm text-muted-foreground">
                      <span>{project.members} members</span>
                      <span>{project.recordings}/{project.totalSentences} recordings</span>
                    </div>
                    <div className="flex gap-2">
                      <Link href={`/projects/${project.id}`}>
                        <Button className="flex-1" size="sm">
                          Continue
                        </Button>
                      </Link>
                      <Button variant="outline" size="sm">
                        Settings
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </div>
      </div>
    </Layout>
  );
}
