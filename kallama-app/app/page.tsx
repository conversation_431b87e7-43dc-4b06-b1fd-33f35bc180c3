'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Plus } from 'lucide-react';
import Layout from '@/components/Layout';
import Link from 'next/link';

export default function Home() {
  const projects = [
    {
      id: 1,
      name: "Yoruba Voice Collection",
      description: "Help build the largest open-source Yoruba speech dataset for AI applications",
      language: "Yoruba",
      dialect: "Lagos",
      type: "Audio",
      participants: 245,
      recordings: 12500,
      isPublic: true
    },
    {
      id: 2,
      name: "Swahili Text Translation",
      description: "Contribute to translating common phrases and sentences into Swahili",
      language: "Swahili",
      dialect: "Standard",
      type: "Translation",
      participants: 189,
      recordings: 8900,
      isPublic: true
    },
    {
      id: 3,
      name: "Hausa Storytelling",
      description: "Record traditional Hausa stories and folktales for cultural preservation",
      language: "Hausa",
      dialect: "Kano",
      type: "Audio",
      participants: 156,
      recordings: 3400,
      isPublic: true
    },
    {
      id: 4,
      name: "Amharic Dictionary",
      description: "Build a comprehensive digital dictionary with audio pronunciations",
      language: "Amharic",
      dialect: "Addis Ababa",
      type: "Text",
      participants: 78,
      recordings: 15600,
      isPublic: true
    }
  ];

  return (
    <Layout showNavigation>
      <div className="space-y-8">
        {/* Header Section */}
        <div className="text-center space-y-4">
          <h1 className="text-4xl font-bold text-primary">Preserve African Languages</h1>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            Join the blockchain-powered movement to collect, preserve, and democratize African language data
          </p>
        </div>

        {/* Action Section */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
          <Link href="/auth/signup">
            <Button size="lg" className="flex items-center gap-2">
              <Plus className="w-4 h-4" />
              Get Started
            </Button>
          </Link>
          <div className="flex gap-2">
            <Input placeholder="Enter invite code" className="w-40" />
            <Button variant="outline">Join Project</Button>
          </div>
        </div>

        {/* Projects Grid */}
        <div className="space-y-4">
          <h2 className="text-2xl font-semibold">Public Projects</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {projects.map((project) => (
              <Card key={project.id} className="hover:shadow-lg transition-shadow">
                <CardHeader>
                  <div className="flex justify-between items-start">
                    <CardTitle className="text-lg">{project.name}</CardTitle>
                    <Badge variant="secondary">{project.type}</Badge>
                  </div>
                  <CardDescription className="text-sm">
                    {project.description}
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex flex-wrap gap-2">
                    <Badge variant="outline">{project.language}</Badge>
                    <Badge variant="outline">{project.dialect}</Badge>
                  </div>
                  <div className="flex justify-between text-sm text-muted-foreground">
                    <span>{project.participants} contributors</span>
                    <span>{project.recordings.toLocaleString()} recordings</span>
                  </div>
                  <Button className="w-full">Join Project</Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Stats Section */}
        <div className="bg-muted/30 rounded-lg p-6">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
            <div>
              <div className="text-2xl font-bold text-primary">50+</div>
              <div className="text-sm text-muted-foreground">Languages</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-primary">25K+</div>
              <div className="text-sm text-muted-foreground">Contributors</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-primary">1M+</div>
              <div className="text-sm text-muted-foreground">Recordings</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-primary">200+</div>
              <div className="text-sm text-muted-foreground">Projects</div>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
}
